"use client";

import { useEffect, useRef, useCallback, useState } from "react";
import { fabric } from "fabric";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Footer } from "@/features/editor/components/footer";
import { Toolbar } from "@/features/editor/components/toolbar";
import { EditableLayer } from "@/types/template";
import debounce from "lodash.debounce";
import { Loader2 } from "lucide-react";

interface CustomizationEditorProps {
  templateData: {
    id: string;
    name: string;
    width: number;
    height: number;
    json: string;
    editableLayers: EditableLayer[];
  };
  customizations: Record<string, string>;
  onCustomizationChange: (layerId: string, value: string) => void;
  onPreviewGenerated: (dataUrl: string) => void;
  activeLayerId?: string | null;
  onLayerActivation?: (layerId: string | null) => void;
}

export const CustomizationEditor = ({
  templateData,
  customizations,
  onCustomizationChange,
  onPreviewGenerated,
  activeLayerId: externalActiveLayerId,
  onLayerActivation,
}: CustomizationEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isCanvasLoading, setIsCanvasLoading] = useState(true);
  const isApplyingCustomizations = useRef(false);

  // Debug: Log when component mounts
  useEffect(() => {
    console.log('CustomizationEditor mounted with templateData:', {
      width: templateData.width,
      height: templateData.height,
      editableLayers: templateData.editableLayers.length,
      hasJson: !!templateData.json,
      jsonLength: templateData.json?.length || 0
    });

    // Try to parse and log JSON structure
    if (templateData.json) {
      try {
        const parsedJson = JSON.parse(templateData.json);
        console.log('Template JSON structure:', {
          version: parsedJson.version,
          objectCount: parsedJson.objects?.length || 0,
          objects: parsedJson.objects?.map((obj: any) => ({
            type: obj.type,
            id: obj.id,
            name: obj.name
          })) || []
        });
      } catch (error) {
        console.error('Error parsing template JSON:', error);
      }
    }
  }, []);

  // Initialize editor first
  const { init, editor } = useEditor({
    defaultState: templateData.json,
    defaultWidth: templateData.width,
    defaultHeight: templateData.height,
    clearSelectionCallback: () => {
      onLayerActivation?.(null);
    },
    saveCallback: () => {
      // Generate preview when canvas changes
      generatePreview();
    },
  });

  // Debug: Log editor state
  useEffect(() => {
    if (editor) {
      console.log('Editor initialized:', {
        hasCanvas: !!editor.canvas,
        hasAutoZoom: !!editor.autoZoom,
        canvasObjects: editor.canvas?.getObjects().length || 0
      });
    }
  }, [editor]);

  // Generate preview from canvas (defined after editor)
  const generatePreview = useCallback(() => {
    if (!editor?.canvas) return;

    try {
      const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
      if (!workspace) return;

      const dataUrl = editor.canvas.toDataURL({
        format: 'png',
        quality: 0.9,
        multiplier: 0.5,
      });
      onPreviewGenerated(dataUrl);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  }, [editor, onPreviewGenerated]);

  // Generate high-quality download from canvas
  const generateDownload = useCallback((filename: string, quality: number = 1.0, format: string = 'png') => {
    if (!editor?.canvas) {
      console.error('No canvas available for download');
      return;
    }

    try {
      console.log('Generating high-quality download...', {
        canvasObjects: editor.canvas.getObjects().length,
        canvasSize: { width: editor.canvas.width, height: editor.canvas.height }
      });

      // Save current viewport transform
      const currentTransform = editor.canvas.viewportTransform?.slice();

      // Reset viewport transform to get the full canvas
      editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

      // Find the workspace (clip) object to get proper dimensions
      const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
      console.log('Workspace found:', !!workspace, workspace ? {
        left: workspace.left,
        top: workspace.top,
        width: workspace.width,
        height: workspace.height
      } : 'none');

      let dataUrl: string;

      if (workspace) {
        // Get the workspace bounding rectangle which accounts for position and scale
        const workspaceBounds = workspace.getBoundingRect();

        console.log('Workspace bounds (generateDownload):', {
          left: workspaceBounds.left,
          top: workspaceBounds.top,
          width: workspaceBounds.width,
          height: workspaceBounds.height
        });

        // Generate high-quality image with exact workspace bounds
        dataUrl = editor.canvas.toDataURL({
          format: format,
          quality: quality,
          multiplier: 2, // Higher resolution for download
          left: workspaceBounds.left,
          top: workspaceBounds.top,
          width: workspaceBounds.width,
          height: workspaceBounds.height,
        });
      } else {
        // Fallback to full canvas
        console.log('Using full canvas for download');
        dataUrl = editor.canvas.toDataURL({
          format: format,
          quality: quality,
          multiplier: 2,
        });
      }

      // Restore viewport transform
      if (currentTransform) {
        editor.canvas.setViewportTransform(currentTransform);
      }

      console.log('Generated dataURL length:', dataUrl.length);

      // Trigger download
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('Download triggered successfully for file:', filename);
    } catch (error) {
      console.error('Failed to generate download:', error);
    }
  }, [editor]);

  // Expose download function globally for debugging (after it's defined)
  useEffect(() => {
    if (editor) {
      (window as any).debugDownload = () => {
        // Dispatch the same event as the download button
        const event = new CustomEvent('downloadCustomized', {
          detail: {
            filename: 'debug-download.png',
            quality: 1.0,
            format: 'png'
          }
        });
        window.dispatchEvent(event);
      };
    }
  }, [editor]);

  // Initialize canvas (same as main editor)
  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 10;

    const initializeCanvas = () => {
      if (!containerRef.current || !canvasRef.current) {
        console.log('Container or canvas ref not available');
        return false;
      }

      const container = containerRef.current;
      const containerWidth = container.offsetWidth;
      const containerHeight = container.offsetHeight;

      console.log(`Canvas init attempt ${retryCount + 1}/${maxRetries}:`, { containerWidth, containerHeight });

      if (containerWidth === 0 || containerHeight === 0) {
        console.log('Container has zero dimensions, retrying...');
        return false;
      }

      // Ensure minimum dimensions
      const finalWidth = Math.max(containerWidth, 300);
      const finalHeight = Math.max(containerHeight, 200);

      const canvas = new fabric.Canvas(canvasRef.current, {
        controlsAboveOverlay: true,
        preserveObjectStacking: true,
        width: finalWidth,
        height: finalHeight,
        // Configure for public customization - cleaner interface
        selection: true,
        backgroundColor: '#f8f9fa', // Light background to distinguish from workspace
      });

      console.log('Canvas initialized with dimensions:', canvas.getWidth(), 'x', canvas.getHeight());

      init({
        initialCanvas: canvas,
        initialContainer: container,
      });

      return canvas;
    };

    const attemptInit = () => {
      const canvas = initializeCanvas();

      if (!canvas && retryCount < maxRetries) {
        retryCount++;
        const delay = Math.min(100 * retryCount, 1000); // Progressive delay up to 1 second
        console.log(`Canvas init failed, retrying in ${delay}ms...`);
        setTimeout(attemptInit, delay);
      } else if (!canvas) {
        console.error('Failed to initialize canvas after maximum retries');
        setIsCanvasLoading(false);
      }

      return canvas;
    };

    // Start initialization
    const canvas = attemptInit();

    return () => {
      if (canvas) {
        canvas.dispose();
      }
    };
  }, [init]);

  // Add ResizeObserver to handle container size changes
  useEffect(() => {
    if (!containerRef.current || !editor?.canvas) return;

    let resizeTimeout: NodeJS.Timeout;
    let lastWidth = 0;
    let lastHeight = 0;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;

        // Only resize if dimensions actually changed significantly (avoid infinite loops)
        if (width > 0 && height > 0 && editor?.canvas &&
            (Math.abs(width - lastWidth) > 5 || Math.abs(height - lastHeight) > 5)) {

          console.log('Resizing canvas to:', width, 'x', height);
          lastWidth = width;
          lastHeight = height;

          editor.canvas.setDimensions({ width, height });

          // Clear previous timeout and set a new one to debounce autoZoom calls
          clearTimeout(resizeTimeout);
          resizeTimeout = setTimeout(() => {
            if (!isCanvasLoading) {
              customAutoZoom(); // Use custom auto-zoom for public interface
            }
          }, 300);
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      clearTimeout(resizeTimeout);
      resizeObserver.disconnect();
    };
  }, [editor, isCanvasLoading, customAutoZoom]);

  // Custom auto-zoom for public customization - fit workspace to canvas perfectly
  const customAutoZoom = useCallback(() => {
    if (!editor?.canvas || !containerRef.current) return;

    const canvas = editor.canvas;
    const container = containerRef.current;

    // Find the workspace
    const workspace = canvas.getObjects().find((obj: any) => obj.name === "clip");
    if (!workspace) {
      console.log('No workspace found for auto-zoom');
      return;
    }

    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;

    if (containerWidth <= 0 || containerHeight <= 0) {
      console.log('Invalid container dimensions:', { containerWidth, containerHeight });
      return;
    }

    // Get workspace dimensions
    const workspaceWidth = workspace.width || 0;
    const workspaceHeight = workspace.height || 0;

    if (workspaceWidth <= 0 || workspaceHeight <= 0) {
      console.log('Invalid workspace dimensions:', { workspaceWidth, workspaceHeight });
      return;
    }

    // Calculate scale to fit workspace perfectly in container with some padding
    const padding = 40; // Increased padding to prevent edge issues
    const availableWidth = containerWidth - (padding * 2);
    const availableHeight = containerHeight - (padding * 2);

    const scaleX = availableWidth / workspaceWidth;
    const scaleY = availableHeight / workspaceHeight;
    const scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%

    console.log('Custom auto-zoom:', {
      containerSize: { width: containerWidth, height: containerHeight },
      workspaceSize: { width: workspaceWidth, height: workspaceHeight },
      scale: scale
    });

    // Prevent infinite loops by checking if scale is reasonable
    if (scale <= 0 || scale > 10) {
      console.warn('Invalid scale calculated:', scale);
      return;
    }

    // Reset viewport transform first
    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

    // Set zoom to fit workspace
    canvas.setZoom(scale);

    // Center the workspace in the canvas
    const workspaceCenter = workspace.getCenterPoint();
    const canvasCenter = canvas.getCenter();

    const vpt = canvas.viewportTransform;
    if (vpt) {
      vpt[4] = canvasCenter.left - workspaceCenter.x * scale;
      vpt[5] = canvasCenter.top - workspaceCenter.y * scale;
      canvas.setViewportTransform(vpt);
    }

    // Force a single render
    canvas.renderAll();
  }, [editor]);

  // Auto-zoom when editor is ready and has content (only once)
  useEffect(() => {
    if (!editor?.canvas || !isCanvasLoading) return;

    console.log('Editor is ready, triggering custom auto-zoom for public interface');

    // Wait for canvas to be fully loaded with content
    const checkAndAutoZoom = () => {
      const objects = editor.canvas.getObjects();
      console.log(`Canvas has ${objects.length} objects, checking for content...`);

      if (objects.length > 1) { // More than just the workspace
        console.log('Canvas has content, triggering initial auto-zoom');
        // Use standard auto-zoom first, then apply custom adjustments
        if (editor.autoZoom) {
          editor.autoZoom();
          // Then apply workspace hiding and custom positioning
          setTimeout(() => {
            customAutoZoom();
          }, 100);
        }
        setIsCanvasLoading(false);
      } else {
        // If no objects yet, wait a bit more (max 5 seconds)
        const maxRetries = 25; // 25 * 200ms = 5 seconds
        const currentRetry = (checkAndAutoZoom as any).retryCount || 0;

        if (currentRetry < maxRetries) {
          (checkAndAutoZoom as any).retryCount = currentRetry + 1;
          setTimeout(checkAndAutoZoom, 200);
        } else {
          console.warn('Template loading timeout - no content found after 5 seconds');
          setIsCanvasLoading(false);
        }
      }
    };

    // Small delay to ensure canvas is ready
    setTimeout(checkAndAutoZoom, 300);
  }, [editor, isCanvasLoading, customAutoZoom]);

  // Fallback timeout to clear loading state
  useEffect(() => {
    const fallbackTimeout = setTimeout(() => {
      if (isCanvasLoading) {
        console.warn('Fallback: Clearing loading state after 10 seconds');
        setIsCanvasLoading(false);
      }
    }, 10000); // 10 seconds fallback

    return () => clearTimeout(fallbackTimeout);
  }, [isCanvasLoading]);

  // Handle selection changes to notify parent
  useEffect(() => {
    if (!editor?.canvas) return;

    const handleSelectionCreated = (e: fabric.IEvent) => {
      const target = e.target;
      if (target) {
        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);

        // Only allow selection of editable layers
        if (layer) {
          onLayerActivation?.(layerId);
        } else {
          // If a non-editable object was somehow selected, clear the selection
          console.log('Preventing selection of non-editable object:', target.type, layerId);
          editor.canvas.discardActiveObject();
          editor.canvas.renderAll();
        }
      }
    };

    const handleSelectionCleared = () => {
      onLayerActivation?.(null);
    };

    const handleTextChanged = (e: fabric.IEvent) => {
      const target = e.target;
      if (target && target.type === 'textbox') {
        // Skip if this update is coming from the sidebar to prevent circular updates
        if ((target as any)._isUpdatingFromSidebar) {
          return;
        }

        const layerId = (target as any).id;
        const layer = templateData.editableLayers.find(l => l.id === layerId);
        if (layer && layer.type === 'text') {
          const currentText = (target as fabric.Textbox).text || '';
          onCustomizationChange(layerId, currentText);
        }
      }
    };

    const handleMouseDown = (e: fabric.IEvent) => {
      const target = e.target;
      if (target) {
        const layerId = (target as any).id;
        const isEditable = templateData.editableLayers.some(l => l.id === layerId);
        const isWorkspace = (target as any).name === 'clip';

        // Prevent interaction with non-editable objects
        if (!isEditable && !isWorkspace) {
          console.log('Preventing interaction with locked object:', target.type, layerId);
          editor.canvas.discardActiveObject();
          editor.canvas.renderAll();
          if (e.e) {
            e.e.preventDefault();
            e.e.stopPropagation();
          }
          return false;
        }
      }
    };

    editor.canvas.on('selection:created', handleSelectionCreated);
    editor.canvas.on('selection:updated', handleSelectionCreated);
    editor.canvas.on('selection:cleared', handleSelectionCleared);
    editor.canvas.on('text:changed', handleTextChanged);
    editor.canvas.on('mouse:down', handleMouseDown);

    return () => {
      editor.canvas.off('selection:created', handleSelectionCreated);
      editor.canvas.off('selection:updated', handleSelectionCreated);
      editor.canvas.off('selection:cleared', handleSelectionCleared);
      editor.canvas.off('text:changed', handleTextChanged);
      editor.canvas.off('mouse:down', handleMouseDown);
    };
  }, [editor, templateData.editableLayers, onLayerActivation, onCustomizationChange]);

  // Debounced function to apply customizations
  const applyCustomizations = useCallback(
    debounce(() => {
      if (!editor?.canvas || isApplyingCustomizations.current) return;

      isApplyingCustomizations.current = true;
      console.log('Applying customizations:', customizations);

      templateData.editableLayers.forEach(layer => {
      const customValue = customizations[layer.id];
      console.log(`Processing layer ${layer.id} (${layer.type}):`, customValue);

      const canvasObject = editor.canvas.getObjects().find((obj: any) => obj.id === layer.id);
      if (!canvasObject) {
        console.log(`Canvas object not found for layer ${layer.id}`);
        return;
      }

      if (layer.type === 'text' && canvasObject.type === 'textbox') {
        const textbox = canvasObject as fabric.Textbox;
        console.log(`Current text: "${textbox.text}", New text: "${customValue}"`);

        if (customValue !== undefined && textbox.text !== customValue) {
          console.log(`Updating text for ${layer.id} to: "${customValue}"`);
          // Temporarily disable event handlers to prevent circular updates
          const isUpdatingFromSidebar = true;
          (textbox as any)._isUpdatingFromSidebar = isUpdatingFromSidebar;

          textbox.set('text', customValue);
          if (editor?.canvas) {
            editor.canvas.renderAll();
          }

          // Clean up the flag after a short delay
          setTimeout(() => {
            delete (textbox as any)._isUpdatingFromSidebar;
          }, 100);
        }
      } else if (layer.type === 'image' && customValue) {
        console.log(`Replacing image for ${layer.id} with: ${customValue}`);

        // Check if this image is already being processed to prevent infinite loops
        const currentSrc = (canvasObject as fabric.Image).getSrc?.();
        if (currentSrc === customValue) {
          console.log(`Image ${layer.id} already has the correct source, skipping replacement`);
          return;
        }

        // Handle image replacement
        fabric.Image.fromURL(customValue, (img) => {
          if (!editor.canvas) {
            console.error('Canvas not available during image replacement');
            return;
          }

          if (!img) {
            console.error('Failed to load image from URL:', customValue);
            return;
          }

          console.log('Original image object:', canvasObject);
          console.log('New image loaded:', img);

          // Get ALL the original object's properties to preserve exact positioning
          const originalProps = {
            left: canvasObject.left,
            top: canvasObject.top,
            width: canvasObject.width,
            height: canvasObject.height,
            scaleX: canvasObject.scaleX,
            scaleY: canvasObject.scaleY,
            angle: canvasObject.angle,
            originX: canvasObject.originX,
            originY: canvasObject.originY,
            flipX: canvasObject.flipX,
            flipY: canvasObject.flipY,
            opacity: canvasObject.opacity,
            visible: canvasObject.visible,
            shadow: canvasObject.shadow,
            stroke: canvasObject.stroke,
            strokeWidth: canvasObject.strokeWidth,
            fill: canvasObject.fill,
            selectable: canvasObject.selectable,
            evented: canvasObject.evented,
            hasControls: canvasObject.hasControls,
            hasBorders: canvasObject.hasBorders,
            lockMovementX: canvasObject.lockMovementX,
            lockMovementY: canvasObject.lockMovementY,
            lockRotation: canvasObject.lockRotation,
            lockScalingX: canvasObject.lockScalingX,
            lockScalingY: canvasObject.lockScalingY,
            lockUniScaling: canvasObject.lockUniScaling,
          };

          console.log('Preserving original properties:', originalProps);

          // Calculate scale to fit within original bounds while maintaining aspect ratio
          const targetWidth = (originalProps.width || 100) * (originalProps.scaleX || 1);
          const targetHeight = (originalProps.height || 100) * (originalProps.scaleY || 1);

          const scaleX = targetWidth / img.width!;
          const scaleY = targetHeight / img.height!;
          const scale = Math.min(scaleX, scaleY);

          // Apply all original properties to the new image, but keep the new scaling
          img.set({
            ...originalProps,
            scaleX: scale,
            scaleY: scale,
          });

          // Ensure the image is positioned exactly like the original
          img.setCoords();

          // Set the ID using a custom property
          (img as any).id = layer.id;

          // Get the exact position in the layer stack
          const objectIndex = editor.canvas.getObjects().indexOf(canvasObject);
          console.log(`Replacing image at index ${objectIndex}`);

          // Only proceed if we found the object
          if (objectIndex === -1) {
            console.warn(`Cannot find object ${layer.id} in canvas objects`);
            return;
          }

          // Remove old image and insert new one at exact same position
          editor.canvas.remove(canvasObject);
          editor.canvas.insertAt(img, objectIndex, false);

          // Ensure the image is properly initialized before rendering
          try {
            // Update coordinates first
            img.setCoords();

            // Render without controls first
            if (editor?.canvas) {
              editor.canvas.renderAll();
            }

            // Small delay before selecting to avoid the getRetinaScaling error
            setTimeout(() => {
              try {
                // Select the new image to show it's been replaced
                if (editor?.canvas && editor.canvas.getContext() && img.canvas === editor.canvas) {
                  editor.canvas.setActiveObject(img);
                  editor.canvas.renderAll();
                }

                console.log(`Image replaced for ${layer.id} at index ${objectIndex}`, img);
              } catch (selectionError) {
                console.warn('Error selecting new image:', selectionError);
                // Still render the canvas even if selection fails
                if (editor?.canvas) {
                  editor.canvas.renderAll();
                }
              }
            }, 100);
          } catch (renderError) {
            console.error('Error during image replacement:', renderError);
            // Fallback: just render the canvas
            if (editor?.canvas) {
              editor.canvas.renderAll();
            }
          }
        }, {
          crossOrigin: 'anonymous'
        });
      }
    });

    // Reset the flag after processing
    isApplyingCustomizations.current = false;

    // Refresh object locking state after customizations are applied
    refreshObjectLocking();

    // Generate preview after customizations are applied
    setTimeout(() => {
      generatePreview();
    }, 200);
    }, 150), // 150ms debounce - faster response for better UX
    [editor, templateData.editableLayers, customizations, generatePreview]
  );

  // Function to refresh object locking state
  const refreshObjectLocking = useCallback(() => {
    if (!editor?.canvas) return;

    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);
    const allObjects = editor.canvas.getObjects();

    allObjects.forEach((obj: any) => {
      const isEditable = editableLayerIds.includes(obj.id);
      const isWorkspace = obj.name === 'clip';

      if (isWorkspace) {
        // Hide workspace visual elements in public interface
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
        obj.visible = false;
      } else if (!isEditable) {
        // Lock the object - make it non-selectable and non-interactive
        obj.selectable = false;
        obj.evented = false;
        obj.hasControls = false;
        obj.hasBorders = false;
        obj.lockMovementX = true;
        obj.lockMovementY = true;
        obj.lockRotation = true;
        obj.lockScalingX = true;
        obj.lockScalingY = true;
        obj.lockUniScaling = true;
        obj.lockSkewingX = true;
        obj.lockSkewingY = true;
      } else if (isEditable) {
        // Ensure editable objects are interactive
        obj.selectable = true;
        obj.evented = true;
        obj.hasControls = true;
        obj.hasBorders = true;
        // Remove any locks on editable objects
        obj.lockMovementX = false;
        obj.lockMovementY = false;
        obj.lockRotation = false;
        obj.lockScalingX = false;
        obj.lockScalingY = false;
        obj.lockUniScaling = false;
        obj.lockSkewingX = false;
        obj.lockSkewingY = false;
      }
    });

    editor.canvas.renderAll();
  }, [editor, templateData.editableLayers]);

  // Apply customizations from sidebar to canvas
  useEffect(() => {
    applyCustomizations();
  }, [customizations, applyCustomizations]);

  // Listen for download and preview events from parent component
  useEffect(() => {
    const handleDownloadEvent = (event: CustomEvent) => {
      console.log('Download event received in CustomizationEditor:', event.detail);
      const { filename, quality, format } = event.detail;

      // Call generateDownload directly to avoid dependency issues
      if (!editor?.canvas) {
        console.error('No canvas available for download');
        return;
      }

      try {
        console.log('Generating high-quality download...', {
          canvasObjects: editor.canvas.getObjects().length,
          canvasSize: { width: editor.canvas.width, height: editor.canvas.height }
        });

        // Save current viewport transform and zoom
        const currentTransform = editor.canvas.viewportTransform?.slice();
        const currentZoom = editor.canvas.getZoom();

        // Reset viewport transform and zoom to get the full canvas at original scale
        editor.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
        editor.canvas.setZoom(1);

        // Find the workspace (clip) object to get proper dimensions
        const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
        console.log('Workspace found:', !!workspace, workspace ? {
          left: workspace.left,
          top: workspace.top,
          width: workspace.width,
          height: workspace.height,
          scaleX: workspace.scaleX,
          scaleY: workspace.scaleY
        } : 'none');

        let dataUrl: string;

        if (workspace) {
          // Get the workspace bounding rectangle which accounts for position and scale
          const workspaceBounds = workspace.getBoundingRect();

          console.log('Workspace bounds:', {
            left: workspaceBounds.left,
            top: workspaceBounds.top,
            width: workspaceBounds.width,
            height: workspaceBounds.height
          });

          // Generate high-quality image with exact workspace bounds
          dataUrl = editor.canvas.toDataURL({
            format: format,
            quality: quality,
            multiplier: 2, // Higher resolution for download
            left: workspaceBounds.left,
            top: workspaceBounds.top,
            width: workspaceBounds.width,
            height: workspaceBounds.height,
          });
        } else {
          // Fallback: try to find template bounds from all objects
          console.log('No workspace found, calculating bounds from all objects');
          const allObjects = editor.canvas.getObjects().filter((obj: any) => obj.name !== 'clip');

          if (allObjects.length > 0) {
            // Calculate bounding box of all objects
            let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

            allObjects.forEach((obj: any) => {
              const bounds = obj.getBoundingRect();
              minX = Math.min(minX, bounds.left);
              minY = Math.min(minY, bounds.top);
              maxX = Math.max(maxX, bounds.left + bounds.width);
              maxY = Math.max(maxY, bounds.top + bounds.height);
            });

            const boundingWidth = maxX - minX;
            const boundingHeight = maxY - minY;

            console.log('Calculated bounds:', { minX, minY, width: boundingWidth, height: boundingHeight });

            dataUrl = editor.canvas.toDataURL({
              format: format,
              quality: quality,
              multiplier: 2,
              left: minX,
              top: minY,
              width: boundingWidth,
              height: boundingHeight,
            });
          } else {
            // Final fallback to full canvas
            console.log('Using full canvas for download');
            dataUrl = editor.canvas.toDataURL({
              format: format,
              quality: quality,
              multiplier: 2,
            });
          }
        }

        // Restore viewport transform and zoom
        if (currentTransform) {
          editor.canvas.setViewportTransform(currentTransform);
        }
        editor.canvas.setZoom(currentZoom);

        console.log('Generated dataURL length:', dataUrl.length);

        // Trigger download
        const link = document.createElement('a');
        link.href = dataUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('Download triggered successfully for file:', filename);
      } catch (error) {
        console.error('Failed to generate download:', error);
      }
    };

    const handlePreviewEvent = () => {
      console.log('Preview event received in CustomizationEditor');

      // Call generatePreview directly to avoid dependency issues
      if (!editor?.canvas) return;

      try {
        const workspace = editor.canvas.getObjects().find((obj: any) => obj.name === "clip");
        if (!workspace) return;

        const dataUrl = editor.canvas.toDataURL({
          format: 'png',
          quality: 0.9,
          multiplier: 0.5,
        });
        onPreviewGenerated(dataUrl);
      } catch (error) {
        console.error('Failed to generate preview:', error);
      }
    };

    console.log('Setting up event listeners for download and preview');
    window.addEventListener('downloadCustomized', handleDownloadEvent as EventListener);
    window.addEventListener('generatePreview', handlePreviewEvent);

    return () => {
      console.log('Cleaning up event listeners');
      window.removeEventListener('downloadCustomized', handleDownloadEvent as EventListener);
      window.removeEventListener('generatePreview', handlePreviewEvent);
    };
  }, [editor, onPreviewGenerated]);

  // Ensure canvas objects have proper IDs when editor loads
  useEffect(() => {
    if (!editor?.canvas) return;

    const canvas = editor.canvas;
    const editableLayerIds = templateData.editableLayers.map(layer => layer.id);

    console.log('Setting up canvas object IDs for editable layers:', editableLayerIds);

    // Wait a bit for the canvas to be fully loaded
    setTimeout(() => {
      const allObjects = canvas.getObjects();
      console.log(`Canvas loaded with ${allObjects.length} objects`);

      // Log only editable objects for debugging
      const editableObjects = allObjects.filter((obj: any) =>
        editableLayerIds.includes(obj.id) ||
        obj.type === 'textbox' ||
        obj.type === 'image'
      );
      console.log('Editable/relevant objects:', editableObjects.map((obj: any) => ({
        id: obj.id,
        type: obj.type,
        name: obj.name,
        text: obj.type === 'textbox' ? obj.text : undefined
      })));

      // Try to match objects to editable layers by content or position
      templateData.editableLayers.forEach((layer) => {
        let matchedObject = allObjects.find((obj: any) => obj.id === layer.id);

        if (!matchedObject) {
          if (layer.type === 'text') {
            // Try to find by text content if no ID match
            matchedObject = allObjects.find((obj: any) =>
              obj.type === 'textbox' &&
              obj.text === layer.originalValue &&
              !editableLayerIds.includes(obj.id)
            );
          } else if (layer.type === 'image') {
            // For images, try to find by type and position, or just by type if it's the nth image
            const imageObjects = allObjects.filter((obj: any) =>
              obj.type === 'image' && !editableLayerIds.includes(obj.id)
            );

            // Try to match by index (first editable image layer matches first unmatched image object)
            const imageLayerIndex = templateData.editableLayers
              .filter(l => l.type === 'image')
              .indexOf(layer);

            if (imageObjects[imageLayerIndex]) {
              matchedObject = imageObjects[imageLayerIndex];
            }
          }
        }

        if (matchedObject && !(matchedObject as any).id) {
          console.log(`Assigning ID ${layer.id} to ${layer.type} object:`, matchedObject);
          (matchedObject as any).id = layer.id;
        }
      });

      // Fix invalid textBaseline values and lock objects
      console.log('Fixing textBaseline values and locking non-editable objects...');
      allObjects.forEach((obj: any) => {
        const isEditable = editableLayerIds.includes(obj.id);
        const isWorkspace = obj.name === 'clip';

        // Fix invalid textBaseline values for text objects
        if (obj.type === 'textbox' || obj.type === 'text' || obj.type === 'i-text') {
          const textObj = obj as fabric.Text;
          const currentBaseline = (textObj as any).textBaseline;
          if (currentBaseline === 'alphabetical' || currentBaseline === 'alphabetic') {
            console.log(`Fixing invalid textBaseline "${currentBaseline}" to "alphabetic"`);
            (textObj as any).textBaseline = 'alphabetic';
          } else if (currentBaseline &&
                     currentBaseline !== 'top' &&
                     currentBaseline !== 'hanging' &&
                     currentBaseline !== 'middle' &&
                     currentBaseline !== 'alphabetic' &&
                     currentBaseline !== 'ideographic' &&
                     currentBaseline !== 'bottom') {
            console.log(`Fixing invalid textBaseline "${currentBaseline}" to "alphabetic"`);
            (textObj as any).textBaseline = 'alphabetic';
          }
        }

        if (isWorkspace) {
          // Hide workspace visual elements in public interface for cleaner look
          obj.selectable = false;
          obj.evented = false;
          obj.hasControls = false;
          obj.hasBorders = false;
          obj.visible = false; // Hide the workspace rectangle completely
          console.log('Hidden workspace object for cleaner public interface');
        } else if (!isEditable) {
          // Lock the object - make it non-selectable and non-interactive
          obj.selectable = false;
          obj.evented = false;
          obj.hasControls = false;
          obj.hasBorders = false;
          obj.lockMovementX = true;
          obj.lockMovementY = true;
          obj.lockRotation = true;
          obj.lockScalingX = true;
          obj.lockScalingY = true;
          obj.lockUniScaling = true;
          obj.lockSkewingX = true;
          obj.lockSkewingY = true;

          console.log(`Locked non-editable object: ${obj.type} (ID: ${obj.id || 'no-id'})`);
        } else if (isEditable) {
          // Ensure editable objects are interactive
          obj.selectable = true;
          obj.evented = true;
          obj.hasControls = true;
          obj.hasBorders = true;

          console.log(`Enabled interaction for editable object: ${obj.type} (ID: ${obj.id})`);
        }
      });

      try {
        canvas.renderAll();

        // Only trigger autoZoom if canvas is still loading (prevent multiple calls)
        if (isCanvasLoading) {
          setTimeout(() => {
            if (isCanvasLoading) {
              console.log('Auto-zooming after template load and ID assignment');
              // Use standard auto-zoom first for stability
              if (editor?.autoZoom) {
                editor.autoZoom();
                // Then apply custom adjustments
                setTimeout(() => {
                  customAutoZoom();
                }, 100);
              }
              // Ensure loading state is cleared
              setIsCanvasLoading(false);
            }
          }, 300);
        }
      } catch (error) {
        console.error('Error rendering canvas after ID assignment:', error);
      }
    }, 500);
  }, [editor, templateData.editableLayers]);

  // Function to select object by layer ID (called from parent)
  useEffect(() => {
    if (!editor?.canvas) return;

    console.log('External active layer changed:', externalActiveLayerId);

    if (externalActiveLayerId) {
      const allObjects = editor.canvas.getObjects();
      console.log('All canvas objects:', allObjects.map((obj: any) => ({ id: obj.id, type: obj.type, name: obj.name })));

      const targetObject = allObjects.find((obj: any) => obj.id === externalActiveLayerId);
      console.log('Found target object:', targetObject);

      if (targetObject) {
        console.log('Selecting object in canvas:', (targetObject as any).id);
        editor.canvas.setActiveObject(targetObject);
        editor.canvas.renderAll();
      } else {
        console.log('Target object not found for ID:', externalActiveLayerId);
      }
    } else {
      // Clear selection if no layer is active
      console.log('Clearing canvas selection');
      editor.canvas.discardActiveObject();
      editor.canvas.renderAll();
    }
  }, [editor, externalActiveLayerId]);

  return (
    <div className="customization-editor-container w-full h-full flex flex-col bg-muted">
      <Toolbar
        editor={editor}
        activeTool="select"
        onChangeActiveTool={() => {}}
        key={JSON.stringify(editor?.canvas.getActiveObject())}
      />
      <div
        className="customization-canvas-container flex-1 bg-white overflow-hidden relative w-full border border-gray-200 rounded-lg shadow-sm mx-4 mb-4"
        ref={containerRef}
        style={{
          minHeight: '400px',
          background: 'linear-gradient(45deg, #f8f9fa 25%, transparent 25%), linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f8f9fa 75%), linear-gradient(-45deg, transparent 75%, #f8f9fa 75%)',
          backgroundSize: '20px 20px',
          backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px'
        }}
      >
        {isCanvasLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted/80 z-10">
            <div className="flex flex-col items-center space-y-3 bg-white p-6 rounded-lg shadow-lg">
              <Loader2 className="h-10 w-10 animate-spin text-blue-600" />
              <div className="text-center">
                <p className="text-lg font-medium text-gray-900">Loading Template</p>
                <p className="text-sm text-gray-600">Please wait while we prepare your editor...</p>
                <p className="text-xs text-gray-500 mt-2">
                  Debug: {editor ? 'Editor ready' : 'Editor loading'} |
                  Objects: {editor?.canvas?.getObjects().length || 0}
                </p>
              </div>
            </div>
          </div>
        )}
        <canvas
          ref={canvasRef}
          className="block w-full h-full"
          style={{
            display: 'block',
            margin: '0 auto',
            maxWidth: '100%',
            maxHeight: '100%',
          }}
        />
      </div>
      <Footer editor={editor} />
    </div>
  );
};
